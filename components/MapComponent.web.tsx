import React from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import { Center } from './StyledView';
import { TitleText, BodyText } from './StyledText';
import { Alarm, LocationCoordinates, MapRegion } from '@/types';

const { width, height } = Dimensions.get('window');

interface MapComponentProps {
  region: MapRegion;
  alarms: Alarm[];
  selectedLocation: LocationCoordinates | null;
  defaultRadius: number;
  onRegionChange: (region: MapRegion) => void;
  onMapPress: (event: any) => void;
}

// Web fallback component
export default function MapComponent({ alarms }: MapComponentProps) {
  return (
    <Center style={styles.map} backgroundColor="gray100">
      <TitleText marginBottom="m">Map View</TitleText>
      <BodyText textAlign="center" marginHorizontal="l">
        Maps are not available on web. Please use the mobile app to create location-based alarms.
      </BodyText>
      <BodyText marginTop="l" textAlign="center" color="textSecondary">
        Current alarms: {alarms.length}
      </BodyText>
    </Center>
  );
}

const styles = StyleSheet.create({
  map: {
    width: width,
    height: height,
  },
});
