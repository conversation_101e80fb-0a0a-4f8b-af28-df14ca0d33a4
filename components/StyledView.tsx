import { createBox } from '@shopify/restyle';
import { Theme } from '@/constants/Theme';

// Create themed view component using Restyle
const StyledView = createBox<Theme>();

export default StyledView;

// Export specific view variants for convenience
export const Card = (props: React.ComponentProps<typeof StyledView>) => (
  <StyledView variant="default" {...props} />
);

export const ElevatedCard = (props: React.ComponentProps<typeof StyledView>) => (
  <StyledView variant="elevated" {...props} />
);

export const Container = (props: React.ComponentProps<typeof StyledView>) => (
  <StyledView flex={1} backgroundColor="background" {...props} />
);

export const Row = (props: React.ComponentProps<typeof StyledView>) => (
  <StyledView flexDirection="row" alignItems="center" {...props} />
);

export const Column = (props: React.ComponentProps<typeof StyledView>) => (
  <StyledView flexDirection="column" {...props} />
);

export const Center = (props: React.ComponentProps<typeof StyledView>) => (
  <StyledView justifyContent="center" alignItems="center" {...props} />
);
