import { createText } from '@shopify/restyle';
import { Theme } from '@/constants/Theme';

// Create themed text component using Restyle
const StyledText = createText<Theme>();

export default StyledText;

// Export specific text variants for convenience
export const HeaderText = (props: React.ComponentProps<typeof StyledText>) => (
  <StyledText variant="header" {...props} />
);

export const TitleText = (props: React.ComponentProps<typeof StyledText>) => (
  <StyledText variant="title" {...props} />
);

export const SubtitleText = (props: React.ComponentProps<typeof StyledText>) => (
  <StyledText variant="subtitle" {...props} />
);

export const BodyText = (props: React.ComponentProps<typeof StyledText>) => (
  <StyledText variant="body" {...props} />
);

export const CaptionText = (props: React.ComponentProps<typeof StyledText>) => (
  <StyledText variant="caption" {...props} />
);

export const SmallText = (props: React.ComponentProps<typeof StyledText>) => (
  <StyledText variant="small" {...props} />
);

export const ButtonText = (props: React.ComponentProps<typeof StyledText>) => (
  <StyledText variant="button" {...props} />
);

// Keep the original MonoText for backward compatibility
import { Text, TextProps } from './Themed';
export function MonoText(props: TextProps) {
  return <Text {...props} style={[props.style, { fontFamily: 'SpaceMono' }]} />;
}
