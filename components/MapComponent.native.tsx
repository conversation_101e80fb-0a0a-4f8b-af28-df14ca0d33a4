import React from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import MapView, { Marker, Circle } from 'react-native-maps';
import { Alarm, LocationCoordinates, MapRegion } from '@/types';

const { width, height } = Dimensions.get('window');

interface MapComponentProps {
  region: MapRegion;
  alarms: Alarm[];
  selectedLocation: LocationCoordinates | null;
  defaultRadius: number;
  onRegionChange: (region: MapRegion) => void;
  onMapPress: (event: any) => void;
}

export default function MapComponent({ 
  region, 
  alarms, 
  selectedLocation, 
  defaultRadius, 
  onRegionChange, 
  onMapPress 
}: MapComponentProps) {
  return (
    <MapView
      style={styles.map}
      region={region}
      onRegionChangeComplete={onRegionChange}
      onPress={onMapPress}
      showsUserLocation={true}
      showsMyLocationButton={true}
    >
      {/* Existing alarms */}
      {alarms.map((alarm) => (
        <React.Fragment key={alarm.id}>
          <Marker
            coordinate={{
              latitude: alarm.latitude,
              longitude: alarm.longitude,
            }}
            title={alarm.name}
            description={`Radius: ${alarm.radius}m`}
            pinColor={alarm.isActive ? 'red' : 'gray'}
          />
          <Circle
            center={{
              latitude: alarm.latitude,
              longitude: alarm.longitude,
            }}
            radius={alarm.radius}
            strokeColor={alarm.isActive ? 'rgba(255, 0, 0, 0.5)' : 'rgba(128, 128, 128, 0.5)'}
            fillColor={alarm.isActive ? 'rgba(255, 0, 0, 0.1)' : 'rgba(128, 128, 128, 0.1)'}
          />
        </React.Fragment>
      ))}

      {/* Selected location for new alarm */}
      {selectedLocation && (
        <React.Fragment>
          <Marker
            coordinate={selectedLocation}
            title="New Alarm Location"
            pinColor="blue"
          />
          <Circle
            center={selectedLocation}
            radius={defaultRadius}
            strokeColor="rgba(0, 122, 255, 0.5)"
            fillColor="rgba(0, 122, 255, 0.1)"
          />
        </React.Fragment>
      )}
    </MapView>
  );
}

const styles = StyleSheet.create({
  map: {
    width: width,
    height: height,
  },
});
