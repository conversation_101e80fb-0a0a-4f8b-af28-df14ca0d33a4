Okay, this is an excellent Product Requirements Document (PRD) for Snozbuz! It's clear, detailed, and covers all the essential aspects. Let's craft the ultimate tech stack using React Native with Expo and then break down the development process step-by-step.

## Snozbuz: The Ultimate React Native (Expo) Tech Stack (2025)

Here’s a recommended tech stack, focusing on robust, well-maintained, and Expo-friendly packages:

**1. Core Framework & Development Environment:**

* **React Native:** The fundamental framework for building native apps with JavaScript/TypeScript.
* **Expo SDK (Latest Version):** Provides a managed workflow, simplifying development, build processes, and access to native APIs. Includes:
    * **EAS (Expo Application Services):**
        * **EAS Build:** For building your `.ipa` and `.apk` files in the cloud.
        * **EAS Submit:** For submitting your app to the Apple App Store and Google Play Store.
        * **EAS Update:** For deploying over-the-air (OTA) updates to your users.
* **TypeScript:** For static typing, improved code quality, and maintainability.
    * Packages: `typescript`, `@types/react`, `@types/react-native`

**2. Navigation:**

* **React Navigation (v6+):** The de-facto standard for routing and navigation in React Native apps.
    * Packages: `@react-navigation/native`, `@react-navigation/native-stack` (for stack navigation, e.g., moving between screens), and potentially `@react-navigation/bottom-tabs` or `@react-navigation/drawer` if you envision that kind of UI structure.

**3. Location Services & Maps:**

* **`expo-location`:** For accessing foreground and background GPS location, geocoding (address to coordinates), and reverse geocoding (coordinates to address). This is crucial for your core functionality.
* **`react-native-maps`:** For displaying interactive maps, allowing users to drop pins, and visualizing their location relative to the destination.
    * Note: While `expo-maps` is an emerging alternative from Expo, `react-native-maps` is currently more feature-rich and widely adopted. It works well with Expo via config plugins if you need advanced map provider features (like Google Maps specific styling not yet in `expo-maps`).
* **`expo-task-manager`:** Essential for defining and managing background tasks, which will be used in conjunction with `expo-location` for reliable background location updates and geofencing.

**4. State Management:**

* **Zustand:** A small, fast, and scalable state management solution. It's simpler than Redux for many use cases and uses hooks effectively, making it a great fit for modern React development.
    * Package: `zustand`

**5. Local Data Storage:**

* **`@react-native-async-storage/async-storage`:** For persisting simple key-value data like user settings (units, default sound), active alarm configurations, and alarm history.
* **`expo-sqlite` (Optional):** If alarm history or other data becomes more complex and requires relational storage, `expo-sqlite` is a good option. For most of Snozbuz's described needs, AsyncStorage should suffice initially.

**6. UI Components & Styling:**

* **React Native Core Components:** `View`, `Text`, `Pressable`, `Modal`, `FlatList`, `TextInput`, etc.
* **Restyle (`@shopify/restyle`):** A type-safe and themeable styling library built on top of React Context. It promotes a consistent design system and makes theming straightforward.
* **`react-native-gesture-handler` & `react-native-reanimated` (v2/v3):** For creating smooth, high-performance animations and handling complex gestures (e.g., for map interactions or custom UI elements). Expo often pre-installs compatible versions.

**7. Notifications & Alarms:**

* **`expo-notifications`:** For scheduling and displaying local notifications (essential for alerting the user when the app is in the background or terminated).
* **`expo-av`:** For playing alarm sounds (custom or predefined) and managing audio playback.
* **`expo-haptics`:** For triggering device vibration patterns.

**8. Permissions & Device Features:**

* Most Expo modules (like `expo-location`, `expo-notifications`) handle their own permission requests, simplifying this process.

**9. Forms (e.g., for Feedback, Naming Alarms):**

* **React Hook Form (`react-hook-form`):** An excellent library for managing form state, validation, and submission with minimal re-renders and easy integration.

**10. API Calls (e.g., for advanced geocoding if needed, or submitting feedback to a server):**

* **Axios:** A popular, promise-based HTTP client for making API requests.
    * Package: `axios`

**11. Utility Libraries:**

* **`date-fns` or `dayjs`:** For any date/time formatting or manipulation (e.g., for alarm history timestamps). `dayjs` is smaller.
* **`uuid`:** For generating unique IDs for alarms.
    * Package: `react-native-uuid` (provides a native implementation for better randomness).

**12. Linting & Formatting:**

* **ESLint:** With plugins for React, React Hooks, and TypeScript (`eslint-plugin-react`, `eslint-plugin-react-hooks`, `@typescript-eslint/eslint-plugin`).
* **Prettier:** For consistent code formatting.

## Step-by-Step App Development Process (TypeScript Files)

Here's a breakdown of the development process, focusing on building one part of the app at a time using best practices. No code will be provided, just the file structure and conceptual steps.

**Project Setup (Initial Steps):**

1.  Initialize your Expo project with TypeScript:
    `npx create-expo-app Snozbuz -t expo-template-tabs` (The tabs template provides a good starting point with TypeScript and React Navigation). If you prefer a blank slate: `npx create-expo-app Snozbuz --template blank-typescript` and then add navigation manually.
2.  Install core dependencies listed above as you need them (e.g., `npm install zustand react-native-maps @react-native-async-storage/async-storage axios react-hook-form @shopify/restyle expo-av expo-haptics react-native-uuid` and `npx expo install expo-location expo-task-manager expo-notifications`).

---

**Phase 1: Core App Structure & Navigation**

* **Goal:** Set up the basic app shell, navigation, and initial screens.
* **Key Files & Folders:**
    * `app/(tabs)/index.tsx` (Main screen, likely the map for setting alarms)
    * `app/(tabs)/activeAlarms.tsx`
    * `app/(tabs)/settings.tsx`
    * `app/_layout.tsx` (Root layout for global providers, navigation setup)
    * `app/(tabs)/_layout.tsx` (Layout for the tab navigator)
    * `components/StyledText.tsx`, `components/StyledView.tsx` (if using Restyle or a similar theming setup)
    * `constants/Colors.ts`, `constants/Theme.ts`
    * `types/navigation.ts` (For React Navigation type safety)
    * `store/alarmStore.ts` (Initial Zustand store setup)
    * `types/index.ts` (For common app types like `Alarm`)
* **Steps:**
    1.  **Define Navigation:** Configure your tab navigator in `app/(tabs)/_layout.tsx`. Define stacks if needed for screens outside the tabs (e.g., an alarm detail/edit screen) in `app/_layout.tsx` or nested stack navigators.
    2.  **Create Placeholder Screens:** Flesh out basic versions of `index.tsx` (Map/Set Alarm), `activeAlarms.tsx`, and `settings.tsx` with placeholder text.
    3.  **Define `Alarm` Type:** In `types/index.ts`, define the structure for an `Alarm` object (e.g., `id: string; name: string; latitude: number; longitude: number; radius: number; isActive: boolean; soundUri: string; vibrationPattern: string;`).
    4.  **Initialize Zustand Store:** Set up `store/alarmStore.ts` with an initial state (e.g., `alarms: Alarm[]`, `settings: UserSettings`) and basic actions (e.g., `addAlarm`, `loadAlarms`).
    5.  **Theming (Restyle - Optional but Recommended):**
        * Define your theme (colors, spacing, typography) in `constants/Theme.ts`.
        * Create themed components like `StyledText`, `StyledView` using Restyle.
        * Wrap your app in the ThemeProvider in `app/_layout.tsx`.

---

**Phase 2: Map Integration & Location Display**

* **Goal:** Display a map and show the user's current location.
* **Key Files & Folders:**
    * `app/(tabs)/index.tsx` (Will become the main map screen)
    * `services/LocationService.ts` (To encapsulate location logic)
* **Steps:**
    1.  **Install `react-native-maps`:** `npx expo install react-native-maps`. Configure API keys for Google Maps if you choose it as a provider in `app.json` under the `expo.android.config.googleMaps.apiKey` and `expo.ios.config.googleMapsApiKey` keys.
    2.  **Request Location Permissions:** In `services/LocationService.ts` or directly in `app/(tabs)/index.tsx`, create a function to request foreground location permissions using `expo-location` (`requestForegroundPermissionsAsync`). Handle different permission states (granted, denied).
    3.  **Fetch Current Location:** Use `expo-location`'s `getCurrentPositionAsync` to get the user's initial coordinates.
    4.  **Render Map:** In `app/(tabs)/index.tsx`, use the `<MapView>` component from `react-native-maps`. Set its `initialRegion` based on the user's current location.
    5.  **Show User Location Marker:** Use the `showsUserLocation` prop on `<MapView>` or a custom `<Marker>` to indicate the user's current position.
    6.  **Real-time Updates (Foreground):** Use `expo-location`'s `watchPositionAsync` to subscribe to location updates while the map screen is active. Update the user's marker or map region accordingly. Remember to unsubscribe when the component unmounts.

---

**Phase 3: Setting a Location Alarm (Map Interaction & Search)**

* **Goal:** Allow users to set a destination by tapping the map or searching.
* **Key Files & Folders:**
    * `app/(tabs)/index.tsx`
    * `components/map/LocationSearchInput.tsx`
    * `components/map/AlarmConfigurationModal.tsx`
* **Steps:**
    1.  **Pin Drop on Map:** Add an `onPress` handler to the `<MapView>` in `app/(tabs)/index.tsx`. On press, get the `coordinate` from the event and store it (e.g., in local component state or a temporary state in your Zustand store). Display a `<Marker>` at this selected coordinate.
    2.  **Location Search Input:**
        * Create `components/map/LocationSearchInput.tsx`. Use a `TextInput`.
        * On submit (or with debouncing as the user types), use `expo-location`'s `geocodeAsync` to convert the address to coordinates.
        * Update the selected coordinate and animate the map to the new location.
    3.  **Alarm Configuration Modal:**
        * Once a location is selected (pin drop or search), display `components/map/AlarmConfigurationModal.tsx`.
        * This modal will contain inputs for:
            * Alarm Name (`TextInput`)
            * Alert Radius (e.g., a slider component or `TextInput` with validation). Display the radius visually on the map if possible (a `<Circle>` component from `react-native-maps`).
            * Alarm Sound (Picker for predefined sounds, later potentially device sounds).
            * Vibration (Toggle or picker for patterns).
    4.  **Save Alarm Action:** On confirming in the modal, gather all data, generate a unique ID (using `react-native-uuid`), create an `Alarm` object, and call the `addAlarm` action in your `alarmStore.ts`.

---

**Phase 4: Background Location Tracking & Geofencing**

* **Goal:** Reliably track location in the background and trigger geofence events. This is the most critical and complex part.
* **Key Files & Folders:**
    * `tasks/locationTask.ts` (This file will be outside the `app` directory, at the root or in a dedicated `tasks` folder)
    * `services/BackgroundLocationService.ts`
* **Steps:**
    1.  **Define Background Task:**
        * In `tasks/locationTask.ts`, use `TaskManager.defineTask(TASK_NAME, async ({ data, error }) => { ... })`.
        * The `TASK_NAME` will be a constant string.
        * Inside this task, you'll handle geofencing events.
    2.  **Request Background Permissions:** In `services/BackgroundLocationService.ts` or when an alarm is activated, request background location permissions (`requestBackgroundPermissionsAsync` from `expo-location`). Clearly explain *why* this is needed for the app's core functionality.
    3.  **Start Geofencing:**
        * When an alarm is set/activated (from `alarmStore.ts` or `BackgroundLocationService.ts`):
            * Retrieve the active alarm's details (latitude, longitude, radius, and its unique ID).
            * Use `expo-location`'s `startGeofencingAsync(TASK_NAME, [{ identifier: alarm.id, latitude: alarm.latitude, longitude: alarm.longitude, radius: alarm.radius, notifyOnEnter: true, notifyOnExit: false }])`.
            * You'll need to manage starting/stopping geofences as alarms are added, removed, or toggled.
    4.  **Handle Geofence Event in Task:**
        * In `tasks/locationTask.ts`, when `TaskManager.defineTask` is invoked due to a geofence event (`data.eventType === Location.GeofencingEventType.Enter`), retrieve the `region.identifier` which corresponds to your `alarm.id`.
        * This is where you will trigger the alarm logic (Phase 5).
    5.  **Battery Optimization & Reliability:**
        * Geofencing is generally more battery-efficient than continuous tracking.
        * Test extensively on various Android and iOS devices/versions. Android often has more aggressive background restrictions.
        * For Android, `expo-location` may require a foreground service notification for persistent background tracking. Configure this via the `foregroundService` option in `startLocationUpdatesAsync` or `startGeofencingAsync` if needed, and use `expo-notifications` to customize it.

---

**Phase 5: Alarm Triggering (Notification, Sound, Vibration)**

* **Goal:** Alert the user when they enter the geofence.
* **Key Files & Folders:**
    * `tasks/locationTask.ts` (Logic to initiate alarm)
    * `services/AlarmTriggerService.ts`
    * `app/alarmRinging.tsx` (A dedicated screen or modal for when an alarm is actively ringing)
* **Steps:**
    1.  **Notification Permissions:** Request notification permissions using `expo-notifications` (`requestPermissionsAsync`) early in the app lifecycle or before setting the first alarm.
    2.  **Trigger Logic (from `tasks/locationTask.ts`):**
        * When a geofence `Enter` event occurs for an alarm ID:
            * Call a function in `services/AlarmTriggerService.ts`.
            * This service will:
                * **Schedule Local Notification:** Use `expo-notifications` (`scheduleNotificationAsync`) to display an immediate, high-priority notification (configure Android notification channels). The notification should state the destination has been reached.
                * **Play Sound:** Use `expo-av` to load and play the selected alarm sound. Ensure it loops or plays until dismissed.
                * **Vibrate:** Use `expo-haptics` to trigger the selected vibration pattern.
    3.  **Alarm Ringing Screen/UI:**
        * The notification should, when tapped, open the app to `app/alarmRinging.tsx` (or a modal overlay).
        * This screen clearly indicates which alarm is ringing and provides a prominent "Dismiss" button.
    4.  **Dismissing the Alarm:**
        * On "Dismiss":
            * Stop sound (`expo-av`).
            * Stop vibration.
            * Clear the notification (`expo-notifications`).
            * Update the alarm's state in `alarmStore.ts` (e.g., mark as `isActive: false`, move to history).
            * Stop the geofence for this specific alarm using `Location.stopGeofencingAsync(TASK_NAME)`. You'll need to be careful if other geofences are active. It's often better to stop all and restart with the remaining active ones, or manage them individually if `expo-location` allows removing specific regions. (Check current API for `stopGeofencingAsync` specifics on regions).

---

**Phase 6: Alarm Management (List, Edit, Delete, Toggle, History)**

* **Goal:** Allow users to manage their created alarms.
* **Key Files & Folders:**
    * `app/(tabs)/activeAlarms.tsx`
    * `components/alarms/AlarmListItem.tsx`
    * `app/editAlarm.tsx` (New screen or modal for editing)
    * `store/alarmStore.ts` (Actions for edit, delete, toggle)
* **Steps:**
    1.  **List Active Alarms:**
        * In `app/(tabs)/activeAlarms.tsx`, subscribe to `alarms` from `alarmStore.ts`.
        * Use a `FlatList` to render `components/alarms/AlarmListItem.tsx` for each alarm where `isActive: true`.
        * Each item should display alarm name, destination, and provide controls.
    2.  **Zustand Actions:**
        * `toggleAlarm(id: string)`: Toggles `isActive`. If toggling on, re-register geofence. If toggling off, unregister geofence.
        * `deleteAlarm(id: string)`: Removes alarm. Unregister geofence.
        * `updateAlarm(id: string, updates: Partial<Alarm>)`: Updates alarm. May need to unregister and re-register geofence if location/radius changes.
    3.  **Edit Alarm Screen:**
        * Navigate to `app/editAlarm.tsx` (passing alarm ID) from `AlarmListItem.tsx`.
        * Pre-fill form with existing alarm data. Allow modification and save using `updateAlarm`.
    4.  **Alarm History:**
        * When an alarm is dismissed, or if `isActive` is false and it's a past event, display it in a separate "History" section or screen.
        * Persist this history using `@react-native-async-storage/async-storage`.

---

**Phase 7: Settings & Customization**

* **Goal:** Implement user-configurable settings.
* **Key Files & Folders:**
    * `app/(tabs)/settings.tsx`
    * `store/settingsStore.ts` (Could be part of `alarmStore.ts` or separate for user preferences)
* **Steps:**
    1.  **Settings State:** Define a slice in your Zustand store for user settings (e.g., `units: 'km' | 'miles'`, `defaultSound: string`, `vibrationEnabled: boolean`). Persist these using AsyncStorage.
    2.  **UI for Settings:**
        * In `app/(tabs)/settings.tsx`, provide UI elements (Switches, Pickers) to modify these settings.
        * **Units:** When units change, ensure all distance displays in the app update.
        * **Alarm Sounds:** Allow selection from pre-loaded sounds (manage these in `assets`).
        * **(Advanced) Location Accuracy Settings:** If implemented, provide options and clear warnings about battery impact. This would adjust parameters passed to `expo-location` functions.

---

**Phase 8: Feedback Mechanism**

* **Goal:** Allow users to send feedback.
* **Key Files & Folders:**
    * `app/feedback.tsx` (New screen)
    * `services/FeedbackService.ts`
* **Steps:**
    1.  **Feedback Screen:** Create `app/feedback.tsx` with a `TextInput` for the message.
    2.  **Submission:**
        * **Simple:** Use `expo-mail-composer` to open the user's email client.
        * **Advanced:** If you have a backend, create `services/FeedbackService.ts` to send the feedback via an API call (using `axios`).

---

**Phase 9: Non-Functional Requirements & Polish**

* **Goal:** Ensure the app is performant, reliable, usable, and secure.
* **Steps:**
    1.  **Performance:**
        * Profile with tools like Flipper or React Native Debugger.
        * Optimize `FlatList`s (`getItemLayout`, `keyExtractor`, `memoized_components`).
        * Use `React.memo`, `useCallback`, `useMemo` judiciously.
        * Monitor battery usage of background tasks closely during testing.
    2.  **Reliability:**
        * Thoroughly test background location and geofencing on physical Android and iOS devices, across different OS versions, and in various network conditions (including offline for core alarm triggering).
        * Implement robust error handling for all async operations (location, storage, API calls).
    3.  **Usability & Accessibility (AX):**
        * Test with screen readers (VoiceOver, TalkBack).
        * Ensure sufficient color contrast and legible font sizes.
        * Ensure all interactive elements are easily tappable.
    4.  **Security & Data Privacy:**
        * Clearly state how location data is used (locally for alarms) in a privacy policy.
        * Request permissions just-in-time and provide clear explanations.
    5.  **Offline Capability:**
        * Core alarm functionality (triggering based on stored alarm data) MUST work offline once an alarm is set and its geofence registered.
        * Map display and address search will naturally require an internet connection. Communicate this to the user if they try to use these features offline.

---

**General Best Practices Throughout Development:**

* **Iterative Testing:** Test on physical devices (both iOS and Android) frequently, especially for location and background features.
* **EAS Usage:** Use EAS Build regularly to create development builds for easier device testing.
* **Clear User Communication:** For permissions, battery usage, and any limitations.
* **Error Handling:** Gracefully handle errors (no GPS, no network, permission denied).
* **Code Reviews & Clean Code:** Maintain a clean, organized, and well-documented codebase.
* **Expo Documentation:** Refer to the official Expo documentation as it's an excellent resource.

This structured approach will help you build Snozbuz methodically, addressing complexities one step at a time. Good luck!