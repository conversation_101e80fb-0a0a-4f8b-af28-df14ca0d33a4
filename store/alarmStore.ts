import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alarm, UserSettings } from '@/types';

interface AlarmStore {
  // State
  alarms: Alarm[];
  settings: UserSettings;
  isLoading: boolean;
  error: string | null;

  // Actions
  addAlarm: (alarm: Omit<Alarm, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateAlarm: (id: string, updates: Partial<Alarm>) => void;
  deleteAlarm: (id: string) => void;
  toggleAlarm: (id: string) => void;
  loadAlarms: () => Promise<void>;
  updateSettings: (settings: Partial<UserSettings>) => void;
  clearError: () => void;
}

const defaultSettings: UserSettings = {
  defaultRadius: 100, // 100 meters
  defaultSound: 'default',
  vibrationEnabled: true,
  notificationsEnabled: true,
  theme: 'auto',
};

export const useAlarmStore = create<AlarmStore>()(
  persist(
    (set, get) => ({
      // Initial state
      alarms: [],
      settings: defaultSettings,
      isLoading: false,
      error: null,

      // Actions
      addAlarm: (alarmData) => {
        const newAlarm: Alarm = {
          ...alarmData,
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        set((state) => ({
          alarms: [...state.alarms, newAlarm],
          error: null,
        }));
      },

      updateAlarm: (id, updates) => {
        set((state) => ({
          alarms: state.alarms.map((alarm) =>
            alarm.id === id
              ? { ...alarm, ...updates, updatedAt: new Date() }
              : alarm
          ),
          error: null,
        }));
      },

      deleteAlarm: (id) => {
        set((state) => ({
          alarms: state.alarms.filter((alarm) => alarm.id !== id),
          error: null,
        }));
      },

      toggleAlarm: (id) => {
        set((state) => ({
          alarms: state.alarms.map((alarm) =>
            alarm.id === id
              ? { ...alarm, isActive: !alarm.isActive, updatedAt: new Date() }
              : alarm
          ),
          error: null,
        }));
      },

      loadAlarms: async () => {
        set({ isLoading: true, error: null });
        try {
          // For now, alarms are persisted automatically by zustand
          // In the future, this could load from a remote API
          set({ isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to load alarms' 
          });
        }
      },

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
          error: null,
        }));
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'alarm-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        alarms: state.alarms,
        settings: state.settings,
      }),
    }
  )
);
