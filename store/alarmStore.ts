import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alarm, UserSettings, LocationCoordinates, MapRegion } from '@/types';
import { locationService, LocationServiceState } from '@/services/LocationService';

interface AlarmStore {
  // State
  alarms: Alarm[];
  settings: UserSettings;
  isLoading: boolean;
  error: string | null;

  // Location state
  currentLocation: LocationCoordinates | null;
  mapRegion: MapRegion | null;
  selectedLocation: LocationCoordinates | null;
  locationPermissionGranted: boolean;
  isLocationLoading: boolean;

  // Actions
  addAlarm: (alarm: Omit<Alarm, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateAlarm: (id: string, updates: Partial<Alarm>) => void;
  deleteAlarm: (id: string) => void;
  toggleAlarm: (id: string) => void;
  loadAlarms: () => Promise<void>;
  updateSettings: (settings: Partial<UserSettings>) => void;
  clearError: () => void;

  // Location actions
  initializeLocation: () => Promise<void>;
  requestLocationPermission: () => Promise<boolean>;
  getCurrentLocation: () => Promise<void>;
  setMapRegion: (region: MapRegion) => void;
  setSelectedLocation: (location: LocationCoordinates | null) => void;
  startLocationTracking: () => Promise<void>;
  stopLocationTracking: () => Promise<void>;
}

const defaultSettings: UserSettings = {
  defaultRadius: 100, // 100 meters
  defaultSound: 'default',
  vibrationEnabled: true,
  notificationsEnabled: true,
  theme: 'auto',
};

export const useAlarmStore = create<AlarmStore>()(
  persist(
    (set, get) => ({
      // Initial state
      alarms: [],
      settings: defaultSettings,
      isLoading: false,
      error: null,

      // Location state
      currentLocation: null,
      mapRegion: null,
      selectedLocation: null,
      locationPermissionGranted: false,
      isLocationLoading: false,

      // Actions
      addAlarm: (alarmData) => {
        const newAlarm: Alarm = {
          ...alarmData,
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          alarms: [...state.alarms, newAlarm],
          error: null,
        }));
      },

      updateAlarm: (id, updates) => {
        set((state) => ({
          alarms: state.alarms.map((alarm) =>
            alarm.id === id
              ? { ...alarm, ...updates, updatedAt: new Date() }
              : alarm
          ),
          error: null,
        }));
      },

      deleteAlarm: (id) => {
        set((state) => ({
          alarms: state.alarms.filter((alarm) => alarm.id !== id),
          error: null,
        }));
      },

      toggleAlarm: (id) => {
        set((state) => ({
          alarms: state.alarms.map((alarm) =>
            alarm.id === id
              ? { ...alarm, isActive: !alarm.isActive, updatedAt: new Date() }
              : alarm
          ),
          error: null,
        }));
      },

      loadAlarms: async () => {
        set({ isLoading: true, error: null });
        try {
          // For now, alarms are persisted automatically by zustand
          // In the future, this could load from a remote API
          set({ isLoading: false });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to load alarms'
          });
        }
      },

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
          error: null,
        }));
      },

      clearError: () => {
        set({ error: null });
      },

      // Location actions
      initializeLocation: async () => {
        set({ isLocationLoading: true, error: null });
        try {
          // Check permissions first
          const permissions = await locationService.checkPermissions();
          set({ locationPermissionGranted: permissions.granted });

          if (permissions.granted) {
            // Get current location
            const location = await locationService.getCurrentLocation();
            const region = locationService.createMapRegion(location);
            set({
              currentLocation: location,
              mapRegion: region,
              isLocationLoading: false
            });
          } else {
            set({ isLocationLoading: false });
          }
        } catch (error) {
          set({
            isLocationLoading: false,
            error: error instanceof Error ? error.message : 'Failed to initialize location'
          });
        }
      },

      requestLocationPermission: async () => {
        set({ isLocationLoading: true, error: null });
        try {
          const permissions = await locationService.requestPermissions();
          set({
            locationPermissionGranted: permissions.granted,
            isLocationLoading: false
          });
          return permissions.granted;
        } catch (error) {
          set({
            isLocationLoading: false,
            error: error instanceof Error ? error.message : 'Failed to request location permission'
          });
          return false;
        }
      },

      getCurrentLocation: async () => {
        set({ isLocationLoading: true, error: null });
        try {
          const location = await locationService.getCurrentLocation();
          const region = locationService.createMapRegion(location);
          set({
            currentLocation: location,
            mapRegion: region,
            isLocationLoading: false
          });
        } catch (error) {
          set({
            isLocationLoading: false,
            error: error instanceof Error ? error.message : 'Failed to get current location'
          });
        }
      },

      setMapRegion: (region) => {
        set({ mapRegion: region });
      },

      setSelectedLocation: (location) => {
        set({ selectedLocation: location });
      },

      startLocationTracking: async () => {
        try {
          await locationService.startWatchingLocation();

          // Subscribe to location updates
          locationService.subscribe((locationState: LocationServiceState) => {
            set({
              currentLocation: locationState.currentLocation,
              locationPermissionGranted: locationState.permissionStatus?.granted || false,
              error: locationState.error
            });
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to start location tracking'
          });
        }
      },

      stopLocationTracking: async () => {
        try {
          await locationService.stopWatchingLocation();
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to stop location tracking'
          });
        }
      },
    }),
    {
      name: 'alarm-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        alarms: state.alarms,
        settings: state.settings,
        // Don't persist location state - it should be fresh on each app start
      }),
    }
  )
);
