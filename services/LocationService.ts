import * as Location from 'expo-location';
import { LocationCoordinates, MapRegion } from '@/types';

export interface LocationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: Location.PermissionStatus;
}

export interface LocationServiceState {
  currentLocation: LocationCoordinates | null;
  permissionStatus: LocationPermissionStatus | null;
  isWatchingLocation: boolean;
  error: string | null;
}

class LocationService {
  private static instance: LocationService;
  private locationSubscription: Location.LocationSubscription | null = null;
  private listeners: ((state: LocationServiceState) => void)[] = [];
  
  private state: LocationServiceState = {
    currentLocation: null,
    permissionStatus: null,
    isWatchingLocation: false,
    error: null,
  };

  private constructor() {}

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  // Subscribe to location service state changes
  subscribe(listener: (state: LocationServiceState) => void): () => void {
    this.listeners.push(listener);
    // Immediately call with current state
    listener(this.state);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.state));
  }

  private updateState(updates: Partial<LocationServiceState>) {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  // Request location permissions
  async requestPermissions(): Promise<LocationPermissionStatus> {
    try {
      this.updateState({ error: null });
      
      const { status, canAskAgain } = await Location.requestForegroundPermissionsAsync();
      
      const permissionStatus: LocationPermissionStatus = {
        granted: status === Location.PermissionStatus.GRANTED,
        canAskAgain,
        status,
      };

      this.updateState({ permissionStatus });
      return permissionStatus;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to request permissions';
      this.updateState({ error: errorMessage });
      throw error;
    }
  }

  // Check current permission status
  async checkPermissions(): Promise<LocationPermissionStatus> {
    try {
      const { status, canAskAgain } = await Location.getForegroundPermissionsAsync();
      
      const permissionStatus: LocationPermissionStatus = {
        granted: status === Location.PermissionStatus.GRANTED,
        canAskAgain,
        status,
      };

      this.updateState({ permissionStatus });
      return permissionStatus;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to check permissions';
      this.updateState({ error: errorMessage });
      throw error;
    }
  }

  // Get current location once
  async getCurrentLocation(): Promise<LocationCoordinates> {
    try {
      this.updateState({ error: null });

      // Check permissions first
      const permissions = await this.checkPermissions();
      if (!permissions.granted) {
        throw new Error('Location permission not granted');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeInterval: 5000,
        distanceInterval: 10,
      });

      const coordinates: LocationCoordinates = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      this.updateState({ currentLocation: coordinates });
      return coordinates;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get current location';
      this.updateState({ error: errorMessage });
      throw error;
    }
  }

  // Start watching location changes
  async startWatchingLocation(): Promise<void> {
    try {
      this.updateState({ error: null });

      // Check permissions first
      const permissions = await this.checkPermissions();
      if (!permissions.granted) {
        throw new Error('Location permission not granted');
      }

      // Stop existing subscription if any
      await this.stopWatchingLocation();

      this.locationSubscription = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000, // Update every 5 seconds
          distanceInterval: 10, // Update when moved 10 meters
        },
        (location) => {
          const coordinates: LocationCoordinates = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          };
          this.updateState({ currentLocation: coordinates });
        }
      );

      this.updateState({ isWatchingLocation: true });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start watching location';
      this.updateState({ error: errorMessage, isWatchingLocation: false });
      throw error;
    }
  }

  // Stop watching location changes
  async stopWatchingLocation(): Promise<void> {
    if (this.locationSubscription) {
      this.locationSubscription.remove();
      this.locationSubscription = null;
    }
    this.updateState({ isWatchingLocation: false });
  }

  // Get current state
  getState(): LocationServiceState {
    return { ...this.state };
  }

  // Utility function to create a map region from coordinates
  createMapRegion(
    coordinates: LocationCoordinates, 
    latitudeDelta: number = 0.01, 
    longitudeDelta: number = 0.01
  ): MapRegion {
    return {
      latitude: coordinates.latitude,
      longitude: coordinates.longitude,
      latitudeDelta,
      longitudeDelta,
    };
  }

  // Calculate distance between two coordinates (in meters)
  calculateDistance(coord1: LocationCoordinates, coord2: LocationCoordinates): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = coord1.latitude * Math.PI / 180;
    const φ2 = coord2.latitude * Math.PI / 180;
    const Δφ = (coord2.latitude - coord1.latitude) * Math.PI / 180;
    const Δλ = (coord2.longitude - coord1.longitude) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  // Clean up resources
  cleanup(): void {
    this.stopWatchingLocation();
    this.listeners = [];
  }
}

// Export singleton instance
export const locationService = LocationService.getInstance();
export default locationService;
