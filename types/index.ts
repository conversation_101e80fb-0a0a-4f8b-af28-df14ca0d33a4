// Core app types
export interface Alarm {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  radius: number; // in meters
  isActive: boolean;
  soundUri?: string;
  vibrationPattern?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSettings {
  defaultRadius: number;
  defaultSound: string;
  vibrationEnabled: boolean;
  notificationsEnabled: boolean;
  theme: 'light' | 'dark' | 'auto';
}

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
}

export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}
