import { NavigatorScreenParams } from '@react-navigation/native';

export type RootStackParamList = {
  '(tabs)': NavigatorScreenParams<TabParamList>;
  modal: undefined;
  alarmDetail: { alarmId: string };
  editAlarm: { alarmId?: string };
};

export type TabParamList = {
  index: undefined;
  activeAlarms: undefined;
  settings: undefined;
};

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
