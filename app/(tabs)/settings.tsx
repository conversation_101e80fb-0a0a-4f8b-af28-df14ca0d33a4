import React from 'react';
import { Switch, ScrollView } from 'react-native';
import FontAwesome from '@expo/vector-icons/FontAwesome';

import { Container, Card, Row, Column } from '@/components/StyledView';
import { TitleText, BodyText, CaptionText } from '@/components/StyledText';
import { useAlarmStore } from '@/store/alarmStore';

export default function SettingsScreen() {
  const { settings, updateSettings } = useAlarmStore();

  const handleToggleSetting = (key: keyof typeof settings, value: boolean) => {
    updateSettings({ [key]: value });
  };

  const SettingRow = ({ 
    icon, 
    title, 
    description, 
    value, 
    onToggle 
  }: {
    icon: string;
    title: string;
    description: string;
    value: boolean;
    onToggle: (value: boolean) => void;
  }) => (
    <Card marginHorizontal="m" marginVertical="s">
      <Row justifyContent="space-between" alignItems="center">
        <Row flex={1} alignItems="center">
          <FontAwesome 
            name={icon as any} 
            size={24} 
            color="#007AFF" 
            style={{ marginRight: 16 }}
          />
          <Column flex={1}>
            <BodyText>{title}</BodyText>
            <CaptionText marginTop="xs">{description}</CaptionText>
          </Column>
        </Row>
        <Switch
          value={value}
          onValueChange={onToggle}
          trackColor={{ false: '#767577', true: '#81b0ff' }}
          thumbColor={value ? '#f5dd4b' : '#f4f3f4'}
        />
      </Row>
    </Card>
  );

  const InfoRow = ({ 
    icon, 
    title, 
    value 
  }: {
    icon: string;
    title: string;
    value: string;
  }) => (
    <Card marginHorizontal="m" marginVertical="s">
      <Row alignItems="center">
        <FontAwesome 
          name={icon as any} 
          size={24} 
          color="#007AFF" 
          style={{ marginRight: 16 }}
        />
        <Column flex={1}>
          <BodyText>{title}</BodyText>
          <CaptionText marginTop="xs">{value}</CaptionText>
        </Column>
      </Row>
    </Card>
  );

  return (
    <Container>
      <ScrollView 
        contentContainerStyle={{ paddingVertical: 16 }}
        showsVerticalScrollIndicator={false}
      >
        <TitleText marginHorizontal="m" marginBottom="l">
          Notification Settings
        </TitleText>
        
        <SettingRow
          icon="bell"
          title="Notifications"
          description="Enable location-based alarm notifications"
          value={settings.notificationsEnabled}
          onToggle={(value) => handleToggleSetting('notificationsEnabled', value)}
        />
        
        <SettingRow
          icon="mobile"
          title="Vibration"
          description="Vibrate when alarm is triggered"
          value={settings.vibrationEnabled}
          onToggle={(value) => handleToggleSetting('vibrationEnabled', value)}
        />

        <TitleText marginHorizontal="m" marginTop="xl" marginBottom="l">
          Default Settings
        </TitleText>
        
        <InfoRow
          icon="circle-o"
          title="Default Radius"
          value={`${settings.defaultRadius} meters`}
        />
        
        <InfoRow
          icon="volume-up"
          title="Default Sound"
          value={settings.defaultSound}
        />
        
        <InfoRow
          icon="paint-brush"
          title="Theme"
          value={settings.theme}
        />

        <TitleText marginHorizontal="m" marginTop="xl" marginBottom="l">
          About
        </TitleText>
        
        <InfoRow
          icon="info-circle"
          title="Version"
          value="1.0.0"
        />
        
        <Card marginHorizontal="m" marginVertical="s">
          <Row alignItems="center">
            <FontAwesome 
              name="heart" 
              size={24} 
              color="#FF3B30" 
              style={{ marginRight: 16 }}
            />
            <Column flex={1}>
              <BodyText>Snozbuz</BodyText>
              <CaptionText marginTop="xs">
                Location-based alarm app to help you never miss your stop
              </CaptionText>
            </Column>
          </Row>
        </Card>
      </ScrollView>
    </Container>
  );
}
