import React from 'react';
import { FlatList, Switch, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import FontAwesome from '@expo/vector-icons/FontAwesome';

import { Container, Card, Row, Column } from '@/components/StyledView';
import { TitleText, BodyText, CaptionText } from '@/components/StyledText';
import { useAlarmStore } from '@/store/alarmStore';
import { Alarm } from '@/types';

export default function ActiveAlarmsScreen() {
  const router = useRouter();
  const { alarms, toggleAlarm, deleteAlarm } = useAlarmStore();

  const handleToggleAlarm = (id: string) => {
    toggleAlarm(id);
  };

  const handleDeleteAlarm = (alarm: Alarm) => {
    Alert.alert(
      'Delete Alarm',
      `Are you sure you want to delete "${alarm.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteAlarm(alarm.id),
        },
      ]
    );
  };

  const handleEditAlarm = (alarmId: string) => {
    router.push(`/editAlarm?alarmId=${alarmId}`);
  };

  const renderAlarmItem = ({ item }: { item: Alarm }) => (
    <Card marginHorizontal="m" marginVertical="s">
      <Row justifyContent="space-between" alignItems="flex-start">
        <Column flex={1}>
          <TitleText numberOfLines={1}>{item.name}</TitleText>
          <CaptionText marginTop="xs">
            Radius: {item.radius}m
          </CaptionText>
          <CaptionText>
            Created: {new Date(item.createdAt).toLocaleDateString()}
          </CaptionText>
        </Column>
        
        <Row alignItems="center" marginLeft="m">
          <Switch
            value={item.isActive}
            onValueChange={() => handleToggleAlarm(item.id)}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={item.isActive ? '#f5dd4b' : '#f4f3f4'}
          />
        </Row>
      </Row>
      
      <Row justifyContent="flex-end" marginTop="m">
        <Row>
          <FontAwesome
            name="edit"
            size={20}
            color="#007AFF"
            style={{ marginRight: 16 }}
            onPress={() => handleEditAlarm(item.id)}
          />
          <FontAwesome
            name="trash"
            size={20}
            color="#FF3B30"
            onPress={() => handleDeleteAlarm(item)}
          />
        </Row>
      </Row>
    </Card>
  );

  const renderEmptyState = () => (
    <Column flex={1} justifyContent="center" alignItems="center" padding="xl">
      <FontAwesome name="bell-slash" size={64} color="#ccc" />
      <TitleText marginTop="l" textAlign="center">
        No Alarms Yet
      </TitleText>
      <BodyText marginTop="s" textAlign="center" color="textSecondary">
        Create your first location-based alarm by tapping the map tab and selecting a location.
      </BodyText>
    </Column>
  );

  return (
    <Container>
      <FlatList
        data={alarms}
        renderItem={renderAlarmItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ 
          flexGrow: 1,
          paddingVertical: 16 
        }}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </Container>
  );
}
