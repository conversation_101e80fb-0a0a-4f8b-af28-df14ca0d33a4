import React from 'react';
import { Platform } from 'react-native';

import { Container, Center } from '@/components/StyledView';
import { TitleText, BodyText } from '@/components/StyledText';
import { useAlarmStore } from '@/store/alarmStore';

export default function MapScreen() {
  const { alarms, addAlarm } = useAlarmStore();

  return (
    <Container>
      {/* Temporary placeholder for map */}
      <Center flex={1} backgroundColor="gray100">
        <TitleText marginBottom="m">Map View</TitleText>
        <BodyText textAlign="center" marginHorizontal="l">
          {Platform.OS === 'web'
            ? 'Maps are not available on web. Please use the mobile app to create location-based alarms.'
            : 'Map functionality will be available here. Tap to create alarms.'
          }
        </BodyText>
        <BodyText marginTop="l" textAlign="center" color="textSecondary">
          Current alarms: {alarms.length}
        </BodyText>
        {alarms.length === 0 && (
          <BodyText marginTop="m" textAlign="center" color="primary" onPress={() => {
            addAlarm({
              name: 'Test Alarm',
              latitude: 37.78825,
              longitude: -122.4324,
              radius: 100,
              isActive: true,
            });
          }}>
            Tap here to create a test alarm
          </BodyText>
        )}
      </Center>
    </Container>
  );
}
