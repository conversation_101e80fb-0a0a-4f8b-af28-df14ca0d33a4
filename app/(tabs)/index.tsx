import React, { useEffect, useState } from 'react';
import { Platform, Alert, ActivityIndicator, TouchableOpacity } from 'react-native';

import StyledView, { Container, Center } from '@/components/StyledView';
import { TitleText, BodyText } from '@/components/StyledText';
import { useAlarmStore } from '@/store/alarmStore';

export default function MapScreen() {
  const {
    alarms,
    addAlarm,
    mapRegion,
    selectedLocation,
    locationPermissionGranted,
    isLocationLoading,
    error,
    initializeLocation,
    requestLocationPermission,
    getCurrentLocation,
    setMapRegion,
    setSelectedLocation,
    startLocationTracking,
    settings
  } = useAlarmStore();

  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeLocation();
        if (locationPermissionGranted) {
          await startLocationTracking();
        }
      } catch (error) {
        console.error('Failed to initialize location:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    initialize();
  }, []);

  const handleRequestPermission = async () => {
    const granted = await requestLocationPermission();
    if (granted) {
      await getCurrentLocation();
      await startLocationTracking();
    } else {
      Alert.alert(
        'Location Permission Required',
        'This app needs location access to create location-based alarms. Please enable location permissions in your device settings.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleMapPress = (event: any) => {
    if (!locationPermissionGranted) return;

    const coordinate = event.nativeEvent.coordinate;
    setSelectedLocation(coordinate);
  };

  const handleCreateAlarm = () => {
    if (!selectedLocation) return;

    addAlarm({
      name: `Alarm at ${selectedLocation.latitude.toFixed(4)}, ${selectedLocation.longitude.toFixed(4)}`,
      latitude: selectedLocation.latitude,
      longitude: selectedLocation.longitude,
      radius: settings.defaultRadius,
      isActive: true,
    });

    setSelectedLocation(null);
    Alert.alert('Success', 'Alarm created successfully!');
  };

  // Show loading while initializing
  if (!isInitialized || isLocationLoading) {
    return (
      <Container>
        <Center flex={1}>
          <ActivityIndicator size="large" />
          <BodyText marginTop="m" textAlign="center">
            {isLocationLoading ? 'Getting your location...' : 'Initializing...'}
          </BodyText>
        </Center>
      </Container>
    );
  }

  // Show web fallback
  if (Platform.OS === 'web') {
    return (
      <Container>
        <Center flex={1} backgroundColor="gray100">
          <TitleText marginBottom="m">Map View</TitleText>
          <BodyText textAlign="center" marginHorizontal="l">
            Maps are not available on web. Please use the mobile app to create location-based alarms.
          </BodyText>
          <BodyText marginTop="l" textAlign="center" color="textSecondary">
            Current alarms: {alarms.length}
          </BodyText>
        </Center>
      </Container>
    );
  }

  // Show permission request if not granted
  if (!locationPermissionGranted) {
    return (
      <Container>
        <Center flex={1} paddingHorizontal="l">
          <TitleText marginBottom="m" textAlign="center">Location Permission Required</TitleText>
          <BodyText textAlign="center" marginBottom="l" color="textSecondary">
            To create location-based alarms, we need access to your location.
          </BodyText>
          <TouchableOpacity onPress={handleRequestPermission}>
            <StyledView
              backgroundColor="primaryLight"
              paddingVertical="m"
              paddingHorizontal="l"
              borderRadius="m"
            >
              <BodyText textAlign="center" color="primary">
                Grant Location Permission
              </BodyText>
            </StyledView>
          </TouchableOpacity>
          {error && (
            <BodyText marginTop="m" textAlign="center" color="error">
              {error}
            </BodyText>
          )}
        </Center>
      </Container>
    );
  }

  // Show map
  return (
    <Container>
      {mapRegion && (
        <MapComponent
          region={mapRegion}
          alarms={alarms}
          selectedLocation={selectedLocation}
          defaultRadius={settings.defaultRadius}
          onRegionChange={setMapRegion}
          onMapPress={handleMapPress}
        />
      )}

      {selectedLocation && (
        <StyledView
          position="absolute"
          bottom={20}
          left={20}
          right={20}
          backgroundColor="white"
          padding="m"
          borderRadius="l"
          shadowColor="black"
          shadowOffset={{ width: 0, height: 2 }}
          shadowOpacity={0.25}
          shadowRadius={4}
          elevation={5}
        >
          <BodyText marginBottom="s" textAlign="center">
            Create alarm at selected location?
          </BodyText>
          <StyledView flexDirection="row" justifyContent="space-between">
            <TouchableOpacity
              onPress={() => setSelectedLocation(null)}
              style={{ flex: 1, marginRight: 8 }}
            >
              <StyledView
                backgroundColor="gray200"
                paddingVertical="s"
                paddingHorizontal="m"
                borderRadius="s"
                alignItems="center"
              >
                <BodyText color="textSecondary">
                  Cancel
                </BodyText>
              </StyledView>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleCreateAlarm}
              style={{ flex: 1, marginLeft: 8 }}
            >
              <StyledView
                backgroundColor="primary"
                paddingVertical="s"
                paddingHorizontal="m"
                borderRadius="s"
                alignItems="center"
              >
                <BodyText color="white">
                  Create Alarm
                </BodyText>
              </StyledView>
            </TouchableOpacity>
          </StyledView>
        </StyledView>
      )}

      {error && (
        <StyledView
          position="absolute"
          top={60}
          left={20}
          right={20}
          backgroundColor="gray100"
          padding="m"
          borderRadius="s"
        >
          <BodyText color="error" textAlign="center">
            {error}
          </BodyText>
        </StyledView>
      )}
    </Container>
  );
}
