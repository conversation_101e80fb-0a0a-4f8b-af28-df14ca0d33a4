{"expo": {"name": "Snozbuz", "slug": "Snozbuz", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "snozbuz", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs access to your location to create location-based alarms.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs access to your location to trigger alarms when you enter or leave specific areas."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow Snozbuz to use your location to create location-based alarms.", "locationAlwaysPermission": "Allow Snozbuz to use your location to trigger alarms when you enter or leave specific areas.", "locationWhenInUsePermission": "Allow Snozbuz to use your location to create location-based alarms.", "isIosBackgroundLocationEnabled": true, "isAndroidBackgroundLocationEnabled": true}]], "experiments": {"typedRoutes": true}}}