{"name": "snozbuz", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/native": "^7.1.6", "@shopify/restyle": "^2.4.5", "and": "^0.0.3", "axios": "^1.9.0", "expo": "~53.0.9", "expo-av": "^15.1.4", "expo-font": "~13.3.1", "expo-haptics": "^14.1.4", "expo-linking": "~7.1.5", "expo-location": "^18.1.5", "expo-notifications": "^0.31.2", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-task-manager": "^13.1.5", "expo-web-browser": "~14.1.6", "install": "^0.13.0", "npx": "^10.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.4", "react-native": "0.79.2", "react-native-maps": "^1.23.8", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-uuid": "^2.0.3", "react-native-web": "~0.20.0", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "jest": "^29.2.1", "jest-expo": "~53.0.5", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}